<template>
	<view class="gt-text-field1" v-show="!hide">
		<u-form-item :label="label" :prop="field" :required="required" labelWidth="auto">
			<u--input v-model="value" :placeholder="placeholder" :suffixIcon="unit" border="surround" clearable @change="changeHandler" :disabled="readonly"
				suffixIconStyle="fontSize: 26rpx"></u--input>
		</u-form-item>
		<view class="gt-text-tips" v-if="tips">{{tips}}</view>
	</view>
</template>

<script>
	import BaseField from '@/components/form/BaseField'

	export default {

		name: 'TextField',

		mixins: [BaseField],

		computed: {
			hide() {
				return this.options.hide === true
			},
			tips(){
				return this.options.tips || ''
			}
		},

		methods: {

			setValue(value, silently = true) {
				this.value = this.genPlaceholderValue(value)
				if (!silently) this.updateFormData()
			},

			changeHand<PERSON>() {
				this.updateFormData()
			}
		}
	}
</script>

<style lang="less" scoped>
	.gt-text-field {
		width: 100%;
	}

	.gt-text-tips {
		font-size: 24rpx;
		color: #8a8a8a;
		position: relative;
		top: -10rpx;
	}
</style>