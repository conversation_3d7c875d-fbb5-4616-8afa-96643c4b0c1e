<template>
  <view class="shadow-md">
    <view class="flex items-center">
      <view class="pl-3">
        <wd-col-picker
          v-if="searchConfig.adminFilter"
          v-model="value"
          :columns="columns"
          @confirm="handleConfirm"
          :column-change="columnChange"
          :use-default-slot="true"
          :z-index="100"
        >
          <wd-button type="text" icon="arrow-down">{{ currentAdminLabel }}</wd-button>
        </wd-col-picker>
      </view>

      <view class="flex-1" v-if="searchConfig && searchConfig.searchFilter">
        <wd-search
          :placeholder="`请输入${searchConfig.searchFilter.label}`"
          @search="searchHandler"
          @clear="searchHandler"
          hide-cancel
        />
      </view>
    </view>
    <view class="" v-if="searchConfig.selectFilters && searchConfig.selectFilters.length">
      <wd-drop-menu>
        <wd-drop-menu-item
          v-for="f in searchConfig.selectFilters"
          :key="f.field"
          v-model="f.value"
          :title="!f.value ? f.label : ''"
          :options="f.options"
          @change="handleChange(f)"
        />
      </wd-drop-menu>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import formData from '@/service/form/formData'
const emit = defineEmits(['change'])
const props = defineProps(['config'])

const useUser = useUserStore()

const searchConfig = reactive({ ...props.config })

const columns = ref([])
const value = ref([])
const defaultLabel = ref('')
const currentAdmin = ref()
const adminMap = {}

const currentAdminLabel = computed(() => {
  const text = currentAdmin.value ? currentAdmin.value.label : defaultLabel.value
  return text.length > 3 ? `${text.slice(0, 3)}...` : text
})

const searchValue = ref()
const searchHandler = (e) => {
  const filter = e && e.value ? ['like', searchConfig.searchFilter.field, `%${e.value}%`] : null
  emit('change', searchConfig.searchFilter.field, filter)
}

const handleChange = (e) => {
  console.log(e)
  console.log('e.op :>> ', e.op)
  const filter = e.value !== null ? [e.op || '=', e.field, e.value] : null

  emit('change', e.field, filter)
}

const init = async () => {
  const config = searchConfig.adminFilter

  const userInfo = useUser.userInfo

  console.log(userInfo, 'userInfo')

  let level = Object.keys(config.adminConfigs)[0]
  defaultLabel.value = userInfo.fname || userInfo.cname || userInfo.pname || config.defaultLabel
  console.log(defaultLabel.value)
  console.log(currentAdminLabel)
  if (userInfo.tcode && config.adminConfigs.village) {
    level = 'village'
  } else if (userInfo.fcode && config.adminConfigs.town) {
    level = 'town'
  } else if (userInfo.ccode && config.adminConfigs.county) {
    level = 'county'
  } else if (userInfo.pcode && config.adminConfigs.city) {
    level = 'city'
  }
  const conf = config.adminConfigs[level]

  const res = await formData.getFormRecords(conf.url, {
    filter:
      conf.filterField && userInfo[conf.filterField]
        ? ['=', conf.filterField, userInfo[conf.filterField]]
        : null,
  })
  const col = [
    ...res.list.map((item) => {
      return {
        ...item,
        label: item[conf.labelkey],
        value: item[conf.valuekey],
        level: conf.level,
        next: conf.next,
        field: conf.field,
      }
    }),
  ]
  console.log(res, col)
  columns.value = [col]
}

const columnChange = async ({ selectedItem, resolve, finish }) => {
  const config = searchConfig.adminFilter
  console.log(selectedItem)
  if (selectedItem.next) {
    const conf = config.adminConfigs[selectedItem.next]
    let col = []
    if (adminMap[selectedItem[conf.filterField]]) {
      col = adminMap[selectedItem.value]
    } else {
      const res = await formData.getFormRecords(conf.url, {
        filter: ['=', conf.filterField, selectedItem.value],
      })
      col = [
        ...res.list.map((item) => {
          return {
            ...item,
            label: item[conf.labelkey],
            value: item[conf.valuekey],
            next: conf.next,
            level: conf.level,
            field: conf.field,
          }
        }),
      ]
      adminMap[selectedItem.value] = col
    }

    resolve(col)
  } else {
    finish()
  }
}

const handleConfirm = ({ selectedItems, value }) => {
  currentAdmin.value = selectedItems.length ? selectedItems[selectedItems.length - 1] : null

  const filter = currentAdmin.value
    ? ['=', currentAdmin.value.field, currentAdmin.value.value]
    : null

  emit('change', 'admin', filter)
  console.log(filter)
}

onMounted(() => {
  init()
})
</script>
<style lang="scss">
.search-wrap {
  height: 80rpx;
}
</style>
