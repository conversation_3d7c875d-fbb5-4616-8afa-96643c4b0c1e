<template>
  <view class="page-container">
    <u-navbar
      title="数据填报"
      :placeholder="true"
      :safe-area-inset-top="true"
      :left-arrow="true"
      @click-left="navigateBack"
    ></u-navbar>

    <scroll-view class="scroll-container" scroll-y>
      <view class="form-content">
        <GTForm v-if="options" :formUid="formUid" :options="options" ref="gtForm"></GTForm>
      </view>
    </scroll-view>
    <view class="btn-list" v-if="!readonly">
      <view class="btn-item">
        <u-button type="info" @click="saveLocalHandler" :custom-style="buttonStyle">暂存</u-button>
      </view>
      <view class="btn-item">
        <u-button type="primary" @click="submitHandler" :loading="submitLoading" :custom-style="buttonStyle">
          立即提交
        </u-button>
      </view>
    </view>
  </view>
</template>

<script>
import GTForm from '@/components/form/GTForm/index.vue'
import { toApiFormData, toFrontFormData } from '@/utils/form'

export default {
  name: 'FillFormPage',
  components: {
    GTForm
  },
  data() {
    return {
      formUid: null,
      cacheRecordId: null,
      formRecordId: null,
      options: null,
      submitLoading: false,
      readonly: false,
      loading: false,
      buttonStyle: {
        width: '100%'
      }
    }
  },
  watch: {
    loading(newValue) {
      if (newValue) {
        uni.showLoading({
          title: '加载中'
        })
      } else {
        uni.hideLoading()
      }
    }
  },
  methods: {
    navigateBack() {
      uni.navigateBack()
    },
    async submitHandler() {
      this.submitLoading = true
      const res = await this.$refs.gtForm.submit()
      console.log(res)
      if (res.state) {
        const values = toApiFormData(res.data)
        if (values._id) {
          await this.updateFormData(this.formUid, values, this.cacheRecordId)
        } else {
          await this.addFormData(this.formUid, values, this.cacheRecordId)
        }
      } else {
        this.submitLoading = false
      }
    },
    async addFormData(formUid, values, cacheRecordId) {
      try {
        await this.$apis.formData.addFormRecord(formUid, values, cacheRecordId)
        uni.showToast({
          icon: 'none',
          title: '数据已提交',
        })
        uni.navigateBack()
      } catch ({ msg, data, code }) {
        switch (code) {
          case 4011:
            break
          default:
            break
        }
      } finally {
        this.submitLoading = false
      }
    },
    async updateFormData(formUid, values, cacheRecordId) {
      try {
        await this.$apis.formData.updateFormRecord(formUid, values, cacheRecordId)
        uni.showToast({
          icon: 'none',
          title: '数据已提交',
        })
        uni.navigateBack()
      } catch ({ msg, data, code }) {
        switch (code) {
          case 4011:
            break
          default:
            break
        }
      } finally {
        this.submitLoading = false
      }
    },
    async saveLocalHandler() {
      const formData = await this.$refs.gtForm.submit()
      console.log(formData, 'saveLocalHandler')
      this.saveLocal(formData)
    },
    // 本地存储
    async saveLocal(formData) {
      if (!formData) return
      const cacheFormRecordId = await this.$apis.formDataLocal.upsertCacheRecord(
        this.formUid,
        this.cacheRecordId,
        formData,
      )
      if (cacheFormRecordId) {
        this.cacheRecordId = cacheRecordId
        uni.showToast({
          icon: 'none',
          title: '数据已缓存',
        })
        // 重置变化检测
      } else {
        console.log('数据缓存失败')
      }
    }
  },
  async onLoad(e) {
    this.formUid = e.formUid
    this.cacheRecordId = e.cacheRecordId
    this.formRecordId = e.formRecordId
    console.log('🔥 :>> ', e)

    try {
      this.loading = true
      const res = await this.$apis.formDesign.getFormDef(this.formUid)
      const opt = { formDef: res, formRecordData: {}, readonly: false }

      if (this.formRecordId) {
        // 获取表单数据
        const result = uni.getStorageSync(this.formRecordId)
        opt.formRecordData = toFrontFormData(result)
      }
      if (this.cacheRecordId) {
        const result = uni.getStorageSync(this.cacheRecordId)
        opt.formRecordData = toFrontFormData(result)
      }
      if (e.readonly === '1') {
        this.readonly = true
        opt.readonly = true
      }
      console.log('opt :>> ', opt)
      this.options = opt
    } catch (error) {
      console.error('加载表单失败:', error)
    } finally {
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  background-color: #f5f5f5;
  flex: 1;
}

.form-content {
  padding-bottom: 48px;
  background-color: white;
}

.btn-list {
  display: flex;
  padding: 8px;
  background-color: white;
  border-top: 1px solid #eee;
}

.btn-item {
  flex: 1;
  margin: 0 8px;
}
</style>
