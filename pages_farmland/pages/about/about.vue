<route lang="json5">
{
  style: {
    navigationBarTitleText: '关于',
  },
}
</route>

<template>
  <view
    class="page-container"
    :style="{ marginTop: safeAreaInsets.top + 'px' }"
  >
    <view class="title-text">
      鸽友们好，我是
      <text class="highlight-text">菲鸽</text>
    </view>
    <RequestComp />
    <UploadComp />
  </view>
</template>

<script>
import RequestComp from './components/request.vue'
import UploadComp from './components/upload.vue'

export default {
  name: 'About',
  components: {
    RequestComp,
    UploadComp
  },
  data() {
    return {
      safeAreaInsets: {}
    }
  },
  mounted() {
    // 获取屏幕边界到安全区域距离
    const systemInfo = uni.getSystemInfoSync()
    this.safeAreaInsets = systemInfo.safeAreaInsets || { top: 0 }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  background-color: white;
  overflow: hidden;
  padding: 8px 16px;
}

.title-text {
  text-align: center;
  font-size: 24px;
  margin-top: 32px;
}

.highlight-text {
  color: #ef4444;
}

.test-css {
  // mt-4=>1rem=>16px;
  margin-top: 16px;
}
</style>
