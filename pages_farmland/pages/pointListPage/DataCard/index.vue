<template>
  <view class="data-card">
    <view class="card-title">
      {{ getText(config.title, data) }}
    </view>
    <view class="status-tag" v-if="config.tag && getTagDepend(config.tag)">
      {{ getText(config.tag.field || config.tag, data) }}
    </view>
    <view class="card-content" v-if="config.columns">
      <view v-for="(col, index) in config.columns" class="content-row" :key="index">
        <text class="field-label">{{ col.label }}:</text>
        <text class="field-value">{{ getText(col.field, data, col.type) }}</text>
      </view>
    </view>
    <view class="additional-info" v-if="config.plusInfo && getPlusInfoDepend(config.plusInfo)">
      <text>
        {{ getText(config.plusInfo.field || config.plusInfo, data) }}
      </text>
    </view>
    <view class="operation-buttons">
      <view
        class="button-wrapper"
        v-for="(operation, index) in config.operations"
        v-show="operationShow(operation, data)"
        @click.stop="operationClickHandler(operation)"
        :key="index"
      >
        <u-button class="action-button" size="small" :type="operation.type || 'primary'">
          {{ operation.label }}
        </u-button>
      </view>
    </view>
    <view class="navigation-arrow">
      <u-icon name="arrow-right" />
    </view>
  </view>
</template>

<script>
import dayjs from "dayjs";

export default {
  name: "DataCard",
  props: {
    config: {
      type: Object,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    userInfo: {
      type: Object,
      required: true,
    },
  },
  methods: {
    getText(k, data, type) {
      const result = k.replace(/{([^}]+)}/g, (match, key) => {
        if (data[key]) {
          switch (type) {
            case "time":
              return dayjs(data[key]).format("YYYY-MM-DD HH:mm:ss");
            default:
              return data[key];
          }
        }
        return "";
      });
      return result;
    },
    getTagDepend(e) {
      if (!e.depends) return true;
      return this.genDependItem(this.data, e.depends);
    },
    getPlusInfoDepend(e) {
      if (!e.depends) return true;
      console.log(e.depends);
      return this.genDependItem(this.data, e.depends);
    },
    genDependItem(data, depend) {
      const type = depend[0];
      const field = depend[1];
      const value = depend[2];
      switch (type) {
        case "=":
          return data[field] === value;
        case "!=":
          return data[field] !== value;
        case "createBy":
          return data[field] === this.userInfo[value] || this.userInfo._id === "1";
        case "roles":
          return this.userInfo.roles.map((item) => item[field]).indexOf(value) !== -1;
        case "user":
          return this.userInfo[field] === value;
        case "in":
          return value.indexOf(data[field]) !== -1;
        default:
          return true;
      }
    },
    operationShow(operation, data) {
      if (!operation.depends) return true;
      const { depends } = operation;
      const type = depends[0];
      if (["and", "or"].indexOf(type) !== -1) {
        const res = depends.slice(1).map((depend) => {
          return this.genDependItem(data, depend);
        });
        if (type === "and") {
          return res.every((i) => i);
        }
        if (type === "any") {
          return res.some((i) => i);
        }
      } else {
        return this.genDependItem(data, depends);
      }
    },
    operationClickHandler(op) {
      const { key, label, confirm } = op;
      if (confirm) {
        uni.showModal({
          title: "提示",
          content: `是否确认执行${label}操作`,
          success: (e) => {
            if (e.confirm) this.$emit("operation", { key, data: this.data });
          },
        });
      } else {
        this.$emit("operation", { key, data: this.data });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/* 主卡片容器 */
.data-card {
  background-color: white;
  border-radius: 8px;
  padding: 12px;
  padding-right: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

/* 卡片标题 */
.card-title {
  color: #3d73ff;
  margin-bottom: 4px;
  font-size: 16px;
  font-weight: bold;
}

/* 状态标签 */
.status-tag {
  position: absolute;
  top: 0;
  right: 0;
  padding: 2px 12px;
  z-index: 10;
  color: white;
  font-size: 0.8rem;
  background-color: #3d73ff;
}

/* 卡片内容区域 */
.card-content {
  position: relative;
  margin-bottom: 15rpx;
  font-size: 0.8rem;
}

/* 内容行 */
.content-row {
  display: flex;
  flex-direction: row;
  margin-bottom: 4px;
  font-weight: normal;
}

/* 字段标签 */
.field-label {
  min-width: 5rem;
  color: #333333;
  white-space: nowrap;
}

/* 字段值 */
.field-value {
  color: #333333;
  margin: 0 8px;
}

/* 附加信息 */
.additional-info {
  margin-bottom: 8px;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.8rem;
  color: #010101;
  background-color: #eeeeee;
}

/* 操作按钮区域 */
.operation-buttons {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

/* 按钮包装器 */
.button-wrapper {
  margin-left: 1rem;
}

/* 操作按钮 */
.action-button {
  height: 100%;
  padding: 0 1rem;
  font-size: 0.85rem;
  line-height: 1.7rem;
}

/* 导航箭头 */
.navigation-arrow {
  position: absolute;
  top: calc(50% - 12px);
  right: 10rpx;
}
</style>
