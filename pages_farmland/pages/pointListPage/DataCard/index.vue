<template>
  <view class="card bg-white rounded p-3 pr-5 shadow-md overflow-hidden position-relative">
    <view class="title mb-1 text-base font-black">
      {{ getText(config.title, data) }}
    </view>
    <view
      class="tag pos-absolute top-0 right-0 px-3 py-0.25 z-10 text-white"
      v-if="config.tag && getTagDepend(config.tag)"
    >
      {{ getText(config.tag.field || config.tag, data) }}
    </view>
    <view class="content" v-if="config.columns">
      <view
        v-for="(col, index) in config.columns"
        class="col flex flex-row mb-1 font-normal"
        :key="index"
      >
        <text class="label">{{ col.label }}:</text>
        <text class="value mx-2">{{ getText(col.field, data, col.type) }}</text>
      </view>
    </view>
    <view
      class="plus-info mb-2 rounded px-2 py-1"
      v-if="config.plusInfo && getPlusInfoDepend(config.plusInfo)"
    >
      <text>
        {{ getText(config.plusInfo.field || config.plusInfo, data) }}
      </text>
    </view>
    <view class="op flex flex-row justify-end">
      <view
        class="op-item"
        v-for="(operation, index) in config.operations"
        v-show="operationShow(operation, data)"
        @click.stop="operationClickHandler(operation)"
        :key="index"
      >
        <wd-button
          class="btn"
          size="small"
          :type="operation.type || 'primary'"
          :icon="operation.icon || ''"
        >
          {{ operation.label }}
        </wd-button>
      </view>
    </view>
    <view class="arrow">
      <wd-icon name="arrow-right" />
    </view>
  </view>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
const props = defineProps(['config', 'data', 'userInfo'])
const emit = defineEmits(['operation'])
const getText = (k, data, type?: string) => {
  const result = k.replace(/{([^}]+)}/g, (match, key) => {
    if (data[key]) {
      switch (type) {
        case 'time':
          return dayjs(data[key]).format('YYYY-MM-DD HH:mm:ss')
        default:
          return data[key]
      }
    }
    return ''
  })
  return result
}
const getTagDepend = (e) => {
  if (!e.depends) return true
  return genDependItem(props.data, e.depends)
}
const getPlusInfoDepend = (e) => {
  if (!e.depends) return true
  console.log(e.depends)
  return genDependItem(props.data, e.depends)
}
const genDependItem = (data, depend) => {
  const type = depend[0]
  const field = depend[1]
  const value = depend[2]
  switch (type) {
    case '=':
      return data[field] === value
    case '!=':
      return data[field] !== value
    case 'createBy':
      return data[field] === props.userInfo[value] || props.userInfo._id === '1'
    case 'roles':
      return props.userInfo.roles.map((item) => item[field]).indexOf(value) !== -1
    case 'user':
      return props.userInfo[field] === value
    case 'in':
      return value.indexOf(data[field]) !== -1
    default:
      return true
  }
}
const operationShow = (operation, data) => {
  if (!operation.depends) return true
  const { depends } = operation
  const type = depends[0]
  if (['and', 'or'].indexOf(type) !== -1) {
    const res = depends.slice(1).map((depend) => {
      return genDependItem(data, depend)
    })
    if (type === 'and') {
      return res.every((i) => i)
    }
    if (type === 'any') {
      return res.some((i) => i)
    }
  } else {
    return genDependItem(data, depends)
  }
}

const operationClickHandler = (op) => {
  const { key, label, confirm } = op
  if (confirm) {
    uni.showModal({
      title: '提示',
      content: `是否确认执行${label}操作`,
      success: async (e) => {
        if (e.confirm) emit('operation', { key, data: props.data })
      },
    })
  } else {
    emit('operation', { key, data: props.data })
  }
}
</script>

<style lang="scss" scoped>
.card {
  .title {
    color: #3d73ff;
  }
  .tag {
    font-size: 0.8rem;
    background-color: #3d73ff;
  }
  .content {
    position: relative;
    margin-bottom: 15rpx;
    font-size: 0.8rem;
    .col {
      .label {
        min-width: 5rem;
        color: #333333;
        white-space: nowrap;
      }
      .value {
        color: #333333;
      }
    }
  }
  .plus-info {
    font-size: 0.8rem;
    color: #010101;
    background-color: #eeeeee;
  }
  .op {
    .op-item {
      margin-left: 1rem;
      .btn {
        height: 100%;
        padding: 0 1rem;
        font-size: 0.85rem;
        line-height: 1.7rem;
      }
    }
  }
}
.arrow {
  position: absolute;
  top: calc(50% - 12px);
  right: 10rpx;
}
</style>
