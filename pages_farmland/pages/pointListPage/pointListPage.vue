<template>
  <view class="page-container">
    <u-navbar
      :title="title || '数据列表'"
      :placeholder="true"
      :safe-area-inset-top="true"
      :left-arrow="true"
      @click-left="navigateBack"
    >
      <template #right>
        <u-icon name="map" size="18" @click="navigateToMapPage" />
      </template>
    </u-navbar>

    <SearchBar :config="searchConfig" v-if="searchConfig" @change="searchChangeHandler" />
    <scroll-view class="scroll-container" scroll-y @scrolltolower="loadMoreData">
      <view class="list-content">
        <view class="card-item" v-for="item in dataList" :key="item._id">
          <DataCard
            :data="item"
            :userInfo="userInfo"
            :config="cardConfig"
            @operation="operationHandler"
            @click="itemClickHandler(item)"
          ></DataCard>
        </view>
      </view>
    </scroll-view>
    <view class="cache-badge" v-if="cacheDataCount">
      <u-badge :count="cacheDataCount">
        <u-button size="small" @click="toCacheDataList">暂存数据</u-button>
      </u-badge>
    </view>
  </view>
</template>

<script lang="ts" setup>
import formData from '@/service/form/formData'
import formDataLocal from '@/service/local/formDataLocal'
import DataCard from './DataCard/index.vue'
import SearchBar from '@/components/common/SearchBar/index.vue'
import { useBasePage } from '@/pages/useBasePage'
import { useUserStore, useStorageDataStore } from '@/store'
import { CARD_CONFIG, SEARCH_CONFIG } from './list.config'
import { useToast } from '@/uni_modules/wot-design-uni'
import bpmApi from '@/service/form/bpm'

const useStorageData = useStorageDataStore()
const useUser = useUserStore()
const userInfo = useUser.userInfo

const { show: showToast } = useToast()
const { navigateBack } = useBasePage()

defineOptions({
  name: 'formListPage',
})

let formUid
let pageLoaded = false
const pageParam = {
  pageNum: 1,
  pageSize: 10,
}

const title = ref()
const cardConfig = ref()
const searchConfig = ref()
const dataList = ref([])
const filterMap: Record<string, any> = {}
const total = ref()
const cacheDataCount = ref()
const createByMyself = ref(false)

const { loading, run } = useRequest((param) => formData.getFormRecords(formUid, param))

// 监听 loading 状态的变化
watch(loading, (newValue) => {
  if (newValue) {
    uni.showLoading({
      title: '加载中', // 替换为实际的加载提示文字
    })
  } else {
    uni.hideLoading()
  }
})
let projectId
async function getDataList(reload?) {
  let filter = [['=', 'project_id', projectId]]

  Object.keys(filterMap).forEach((k) => {
    if (filterMap[k]) filter.push(filterMap[k])
  })

  if (filter.length >= 2) filter.unshift('and')

  if (filter.length === 1) filter = filter[0]

  if (!filter.length) filter = null

  // const res = await run({ filter, page: pageParam })
  const res = await formData.joinSearchFormData('sampling_task', {
    filter,
    // filter: ['and', ['=', 'task_status', '已下发'], ['=', 'project_id', projectId]],
    page: pageParam,
    joins: [
      {
        type: 'left',
        table: 'point_info', // 联查表单名称
        joinedTable: 'sampling_task', // 主要表单名称
        // filter,
        multiJoinFields: [
          {
            joinedField: 'sampling_point_id', // 主要表单的键名
            field: '_id', // 联查表单的键名
          },
        ],
      },
    ],
  })

  for (let i = 0; i < res.list.length; i++) {
    const item = res.list[i]
    if (
      ['采样单位已退回', '县级管理员已退回', '市级管理员已退回', '省级管理员已退回'].indexOf(
        item['sampling_task.audit_status'],
      ) !== -1
    ) {
      const r = await bpmApi.viewProcess('sampling_task', item['sampling_task._id'])
      console.log('r :>> ', r)
      const comment = r[r.length - 1].filter((f) => {
        return !f.approved
      })
      console.log('comment :>> ', comment)
      item.return_comment = comment[comment.length - 1].comment
    }
  }

  total.value = res.total
  if (reload) {
    dataList.value = [...res.list]
  } else {
    dataList.value.push(...res.list)
  }
}

async function loadMoreData() {
  console.log('loadMoreData')
  pageParam.pageNum += 1
  await getDataList()
}

async function reloadData() {
  pageParam.pageNum = 1
  await getDataList(true)
  pageLoaded = true
}

async function operationHandler({ key, data }) {
  console.log(key, data)
  switch (key) {
    case 'edit':
      editRecordHandler(data)
      break
    case 'view':
      viewRecordHandler(data)
      break
    case 'delete':
      deleteRecordHandler(data)
      break
    default:
      break
  }
}

async function searchChangeHandler(field, filter) {
  console.log(field, filter)
  filterMap[field] = filter
  await reloadData()
}

async function createByMyselfHandler() {
  createByMyself.value = !createByMyself.value
  console.log('userInfo :>> ', userInfo)
  filterMap.createBy = createByMyself.value ? ['=', 'create_by', userInfo.user_id] : null

  await reloadData()
}

async function getCacheData() {
  const res = await formDataLocal.countCacheRecords(formUid)
  console.log(res, 'cache')
  cacheDataCount.value = res
}

async function editRecordHandler(record) {
  const formRecordId = record._id
  useStorageData.setStorageData(formRecordId, record)
  uni.navigateTo({
    url: `/pages/formPage/formPage?formUid=${formUid}&formRecordId=${formRecordId}`,
  })
}

async function viewRecordHandler(record) {
  const formRecordId = record._id
  useStorageData.setStorageData(formRecordId, record)
  uni.navigateTo({
    url: `/pages/formPage/formPage?formUid=${formUid}&formRecordId=${formRecordId}&readonly=1`,
  })
}

async function deleteRecordHandler(record) {
  try {
    await formData.deleteFormDataById(formUid, record._id)
    await reloadData()
  } catch (error) {
    showToast('删除失败')
  }
}

function navigateToMapPage() {
  uni.navigateTo({
    url: `/pages/pointMapPage/pointMapPage?projectId=${projectId}`,
  })
}

function itemClickHandler(item) {
  console.log('item :>> ', item)
  const id = item['sampling_task._id']
  const type = item['sampling_task.sampling_task_type']
  const record = {}
  const pointInfo = {}
  Object.keys(item).forEach((k) => {
    if (k.split('.')[0] === 'sampling_task') {
      record[k.split('.')[1]] = item[k]
    }
    if (k.split('.')[0] === 'point_info') {
      pointInfo[k.split('.')[1]] = item[k]
    }
  })

  record.pointInfo = pointInfo

  useStorageData.setStorageData(id, record)
  uni.navigateTo({
    url: `/pages/pointFillPage/pointFillPage?taskId=${id}&formUid=${type === '农产品' ? 'ncpcjxxb' : 'trcjxxb'}`,
  })
}

onShow(async () => {
  if (pageLoaded) {
    await getCacheData()
    await reloadData()
  }
})

onLoad(async (e) => {
  console.log(e)

  formUid = ''
  if (e.title) {
    title.value = e.title
  }
  if (e.dataId) {
    projectId = e.dataId
  }
  const conf = useStorageData.getStorageData(formUid)
  console.log(conf)

  cardConfig.value = CARD_CONFIG

  searchConfig.value = SEARCH_CONFIG

  await getCacheData()
  await reloadData()
})
</script>

<style lang="scss" scoped></style>
