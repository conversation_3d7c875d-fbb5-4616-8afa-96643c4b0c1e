<template>
  <view class="page-container">
    <u-navbar :title="title || '数据列表'" :placeholder="true" :safe-area-inset-top="true" :left-arrow="true" @leftClick="navigateBack">
      <template #right>
        <u-icon name="map" size="18" @click="navigateToMapPage" />
      </template>
    </u-navbar>

    <SearchBar :config="searchConfig" v-if="searchConfig" @change="searchChangeHandler" />
    <scroll-view class="scroll-container" scroll-y @scrolltolower="loadMoreData">
      <view class="list-content">
        <view class="card-item" v-for="item in dataList" :key="item._id">
          <DataCard :data="item" :userInfo="userInfo" :config="cardConfig" @operation="operationHandler" @click="itemClickHandler(item)"></DataCard>
        </view>
      </view>
    </scroll-view>
    <view class="cache-badge" v-if="cacheDataCount">
      <u-badge :count="cacheDataCount">
        <u-button size="small" @click="toCacheDataList">暂存数据</u-button>
      </u-badge>
    </view>
  </view>
</template>

<script>
import DataCard from "./DataCard/index.vue";
import SearchBar from "@/pages_farmland/components/SearchBar/index.vue";
import { CARD_CONFIG, SEARCH_CONFIG } from "./list.config";

export default {
  name: "pointListPage",
  components: {
    DataCard,
    SearchBar,
  },
  data() {
    return {
      formUid: null,
      pageLoaded: false,
      pageParam: {
        pageNum: 1,
        pageSize: 10,
      },
      title: null,
      cardConfig: null,
      searchConfig: null,
      dataList: [],
      filterMap: {},
      total: 0,
      cacheDataCount: 0,
      createByMyself: false,
      loading: false,
      userInfo: null,
      projectId: null,
    };
  },
  mounted() {
    // 获取用户信息
    this.userInfo = uni.getStorageSync("userInfo") || {};
  },
  onShow() {
    if (this.pageLoaded) {
      this.getCacheData();
      this.reloadData();
    }
  },
  onLoad(e) {
    this.initPage(e);
  },
  watch: {
    loading(newValue) {
      if (newValue) {
        uni.showLoading({
          title: "加载中",
        });
      } else {
        uni.hideLoading();
      }
    },
  },
  methods: {
    navigateBack() {
      uni.navigateBack();
    },
    showToast(message) {
      uni.showToast({
        title: message,
        icon: "none",
      });
    },
    async getDataList(reload) {
      let filter = [["=", "project_id", this.projectId]];

      Object.keys(this.filterMap).forEach((k) => {
        if (this.filterMap[k]) filter.push(this.filterMap[k]);
      });

      if (filter.length >= 2) filter.unshift("and");

      if (filter.length === 1) filter = filter[0];

      if (!filter.length) filter = null;

      try {
        this.loading = true;
        const res = await this.$apis.formData.joinSearchFormData("sampling_task", {
          filter,
          page: this.pageParam,
          joins: [
            {
              type: "left",
              table: "point_info", // 联查表单名称
              joinedTable: "sampling_task", // 主要表单名称
              multiJoinFields: [
                {
                  joinedField: "sampling_point_id", // 主要表单的键名
                  field: "_id", // 联查表单的键名
                },
              ],
            },
          ],
        });

        for (let i = 0; i < res.list.length; i++) {
          const item = res.list[i];
          if (["采样单位已退回", "县级管理员已退回", "市级管理员已退回", "省级管理员已退回"].indexOf(item["sampling_task.audit_status"]) !== -1) {
            const r = await this.$apis.bpm.viewProcess("sampling_task", item["sampling_task._id"]);
            console.log("r :>> ", r);
            const comment = r[r.length - 1].filter((f) => {
              return !f.approved;
            });
            console.log("comment :>> ", comment);
            item.return_comment = comment[comment.length - 1].comment;
          }
        }

        this.total = res.total;
        if (reload) {
          this.dataList = [...res.list];
        } else {
          this.dataList.push(...res.list);
        }
      } catch (error) {
        console.error("获取数据失败:", error);
      } finally {
        this.loading = false;
      }
    },
    async loadMoreData() {
      console.log("loadMoreData");
      this.pageParam.pageNum += 1;
      await this.getDataList();
    },
    async reloadData() {
      this.pageParam.pageNum = 1;
      await this.getDataList(true);
      this.pageLoaded = true;
    },
    async operationHandler({ key, data }) {
      console.log(key, data);
      switch (key) {
        case "edit":
          this.editRecordHandler(data);
          break;
        case "view":
          this.viewRecordHandler(data);
          break;
        case "delete":
          this.deleteRecordHandler(data);
          break;
        default:
          break;
      }
    },
    async searchChangeHandler(field, filter) {
      console.log(field, filter);
      this.filterMap[field] = filter;
      await this.reloadData();
    },
    async createByMyselfHandler() {
      this.createByMyself = !this.createByMyself;
      console.log("userInfo :>> ", this.userInfo);
      this.filterMap.createBy = this.createByMyself ? ["=", "create_by", this.userInfo.user_id] : null;
      await this.reloadData();
    },
    async getCacheData() {
      const res = await this.$apis.formDataLocal.countCacheRecords(this.formUid);
      console.log(res, "cache");
      this.cacheDataCount = res;
    },
    async toCacheDataList() {
      uni.setStorageSync(this.formUid, this.cardConfig);
      uni.navigateTo({
        url: `/pages/cacheDataListPage/cacheDataListPage?formUid=${this.formUid}`,
      });
    },
    async editRecordHandler(record) {
      const formRecordId = record._id;
      uni.setStorageSync(formRecordId, record);
      uni.navigateTo({
        url: `/pages/formPage/formPage?formUid=${this.formUid}&formRecordId=${formRecordId}`,
      });
    },
    async viewRecordHandler(record) {
      const formRecordId = record._id;
      uni.setStorageSync(formRecordId, record);
      uni.navigateTo({
        url: `/pages/formPage/formPage?formUid=${this.formUid}&formRecordId=${formRecordId}&readonly=1`,
      });
    },
    async deleteRecordHandler(record) {
      try {
        await this.$apis.formData.deleteFormDataById(this.formUid, record._id);
        await this.reloadData();
      } catch (error) {
        this.showToast("删除失败");
      }
    },
    navigateToMapPage() {
      uni.navigateTo({
        url: `/pages/pointMapPage/pointMapPage?projectId=${this.projectId}`,
      });
    },
    itemClickHandler(item) {
      console.log("item :>> ", item);
      const id = item["sampling_task._id"];
      const type = item["sampling_task.sampling_task_type"];
      const record = {};
      const pointInfo = {};
      Object.keys(item).forEach((k) => {
        if (k.split(".")[0] === "sampling_task") {
          record[k.split(".")[1]] = item[k];
        }
        if (k.split(".")[0] === "point_info") {
          pointInfo[k.split(".")[1]] = item[k];
        }
      });

      record.pointInfo = pointInfo;

      uni.setStorageSync(id, record);
      uni.navigateTo({
        url: `/pages/pointFillPage/pointFillPage?taskId=${id}&formUid=${type === "农产品" ? "ncpcjxxb" : "trcjxxb"}`,
      });
    },
    async initPage(e) {
      console.log(e);

      this.formUid = "";
      if (e.title) {
        this.title = e.title;
      }
      if (e.dataId) {
        this.projectId = e.dataId;
      }

      this.cardConfig = CARD_CONFIG;
      this.searchConfig = SEARCH_CONFIG;

      await this.getCacheData();
      await this.reloadData();
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  background-color: #f5f5f5;
  flex: 1;
}

.list-content {
  padding-bottom: 48px;
}

.card-item {
  margin: 12px;
}

.cache-badge {
  position: fixed;
  bottom: 40px;
  left: 12px;
}
</style>
