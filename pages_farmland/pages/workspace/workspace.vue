<template>
  <view class="page-container">
    <u-navbar title="工作台" :placeholder="true" :safe-area-inset-top="true"></u-navbar>

    <scroll-view class="scroll-container" scroll-y>
      <view class="task-item" v-for="menu in menuList" :key="menu._id" @click="menuItemClick(menu)">
        <view class="task-item-content">
          <view class="task-item-title">{{ menu.name }}</view>
        </view>
        <u-icon name="arrow-right" />
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: "Home",
  data() {
    return {
      menuList: [
        {
          _id: "1",
          name: "省控监测采样",
          val: "省控监测采样项目",
        },
        {
          _id: "2",
          name: "国控监测采样",
          val: "国控监测采样项目",
        },
        {
          _id: "3",
          name: "其他监测采样",
          val: "其他项目",
        },
      ],
    };
  },
  onShow() {},
  mounted() {},
  methods: {
    menuItemClick(menu) {
      console.log("menu :>> ", menu);
      uni.navigateTo({
        url: `/pages/workspaceMenu/workspaceMenu?menu=${menu.val}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  background-color: #f5f5f5;
  flex: 1;
}

.task-item {
  padding: 16px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 4px;
  margin: 12px;
}

.task-item-content {
  flex: 1;
}

.task-item-title {
  font-size: 16px;
  color: #333;
}

:deep(.loading-black) {
  padding: 10px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
}
</style>
