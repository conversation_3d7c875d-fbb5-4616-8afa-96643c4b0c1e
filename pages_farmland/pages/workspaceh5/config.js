export const WORKSPACE_TABLE = 'app_workspace'

const SUB_FORM_DATA_KEY = '_mis_sub_items'

export const toFrontFormData = function (data) {
  const obj = {}
  Object.keys(data).forEach((key) => {
    if (key === SUB_FORM_DATA_KEY) {
      data[SUB_FORM_DATA_KEY].forEach((k) => {
        obj[k.form] = {
          form: k.form,
          data: k.data
            .map((item) => {
              return toFrontFormData(item)
            })
            .sort((a, b) => {
              if (a.index && b.index) {
                return a.index - b.index
              }
              return 1
            }),
        }
      })
    } else {
      obj[key] = data[key]
    }
  })
  return obj
}
