<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '工作台',
  },
}
</route>
<template>
  <view class="h-screen flex flex-col">
    <wd-navbar title="工作台" placeholder safeAreaInsetTop></wd-navbar>

    <scroll-view class="bg-gray-100" scroll-y>
      <view class="m-3" v-for="group in menuList" :key="group._id">
        <view class="task-group-title">
          <view class="title mb-2 font-bold text-gray-900">{{ group.title }}</view>
        </view>
        <view
          class="task-item p-4 bg-white flex items-center justify-between rounded mb-2"
          v-for="menu in group.app_workspace_child.data"
          :key="menu._id"
          @click="menuItemClick(menu)"
        >
          <view class="task-item-content">
            <view class="task-item-title">{{ menu.name }}</view>
          </view>
          <wd-icon name="arrow-right" />
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import { WORKSPACE_TABLE, toFrontFormData } from './config'
import { useStorageDataStore } from '@/store'

defineOptions({
  name: 'Home',
})

const useStorageData = useStorageDataStore()

const menuList = ref([])

onLoad(async (e) => {
  console.log('e :>> ', e)
  menuList.value = JSON.parse(e.conf)
})

const menuItemClick = (menu) => {
  if (menu.app_workspace_child_forms.data.length >= 2) {
    useStorageData.setStorageData(menu.name, menu.app_workspace_child_forms.data)
    uni.navigateTo({
      url: `/pages_farmland/pages/workspaceMenu/workspaceMenu?menu=${menu.name}`,
    })
  } else if (menu.app_workspace_child_forms.data.length === 1) {
    const obj = menu.app_workspace_child_forms.data[0]

    useStorageData.setStorageData(obj.form_name, obj)
    if (obj.type === 'preset') {
      uni.navigateTo({
        url: `/pages_farmland/pages/taskFormListPage/taskFormListPage?formUid=${obj.form_name}&title=${obj.title}`,
      })
    } else {
      uni.navigateTo({
        url: `/pages_farmland/pages/formListPage/formListPage?formUid=${obj.form_name}&title=${obj.title}`,
      })
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.loading-black) {
  padding: 10px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
}
</style>
