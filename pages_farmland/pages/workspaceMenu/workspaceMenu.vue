<template>
  <view class="page-container">
    <u-navbar :title="title" :placeholder="true" :safe-area-inset-top="true" :left-arrow="true" @leftClick="navigateBack"></u-navbar>
    <scroll-view scroll-y class="scroll-container">
      <view class="task-item" v-for="project in projects" :key="project._id" @click="menuClickHandler(project)">
        <view class="task-item-content">
          <view class="task-item-title">{{ project.project_name }}</view>
        </view>
        <u-icon name="arrow-right" />
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: "WorkspaceMenu",
  data() {
    return {
      title: null,
      projects: [],
    };
  },
  methods: {
    navigateBack() {
      console.log("navigateBack");

      uni.navigateBack();
    },
    menuClickHandler(e) {
      console.log(e);
      uni.setStorageSync(e._id, e);
      uni.navigateTo({
        url: `/pages_farmland/pages_farmland/pages/pointListPage/pointListPage?dataId=${e._id}&title=${e.project_name}`,
      });
    },
    async init() {
      const res = await this.$apis.formData.getFormRecords("project_info", {
        filter: ["=", "project_type", this.title],
      });
      this.projects = res.list;
      console.log("res :>> ", res);
    },
  },
  onLoad(e) {
    this.title = e.menu;
    this.init();
    console.log("e.menu :>> ", e.menu);
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  background-color: #f5f5f5;
  flex: 1;
}

.task-item {
  padding: 16px;
  margin: 12px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.task-item-content {
  flex: 1;
}

.task-item-title {
  font-size: 16px;
  color: #333;
}
</style>
