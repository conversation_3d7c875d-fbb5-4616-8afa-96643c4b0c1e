<template>
  <SearchBar v-if="searchConfig" :config="searchConfig" @change="searchChangeHandler" />

  <view class="info bg-white p-1 px-3 flex justify-between items-center text-xs">
    共{{ total }}条
  </view>
  <scroll-view scroll-y @scrolltolower="loadMoreData" class="bg-gray-100">
    <view class="pb-12">
      <view class="m-3" v-for="item in dataList" :key="item._id">
        <DataCard
          :data="item"
          :config="fillCardConfig"
          :userInfo="userInfo"
          @operation="operationHandler"
        ></DataCard>
      </view>
    </view>
  </scroll-view>
</template>
<script lang="ts" setup>
import formData from '@/service/form/formData'
import DataCard from '@/components/common/DataCard/index.vue'
import SearchBar from '@/components/common/SearchBar/index.vue'
import { useUserStore, useStorageDataStore } from '@/store'
import { omit } from 'lodash-es'

const props = defineProps(['formUid', 'searchConfig', 'cardConfig', 'fillFormUid'])
const emit = defineEmits(['operation'])

const DEFAULT_CARD_CONFIG = {
  title: '{_id}',
  columns: [{ label: 'createTime', field: '{create_time}', type: 'time' }],
  operations: [
    {
      key: 'view',
      label: '查看',
    },
    {
      key: 'fill',
      label: '填报',
    },
  ],
}

const fillCardConfig = computed(() => {
  return props.cardConfig || DEFAULT_CARD_CONFIG
})

const useStorageData = useStorageDataStore()
const useUser = useUserStore()
const userInfo = useUser.userInfo

const pageParam = {
  pageNum: 1,
  pageSize: 10,
}
const total = ref()
const filterMap = {
  state: ['=', 'state', '待填报'],
}

const { loading, run } = useRequest((param) => formData.getFormRecords(props.formUid, param))

const dataList = ref([])

const getDataList = async () => {
  let filter = []

  Object.keys(filterMap).forEach((k) => {
    if (filterMap[k]) filter.push(filterMap[k])
  })

  if (filter.length >= 2) filter.unshift('and')

  if (filter.length === 1) filter = filter[0]

  if (!filter.length) filter = null

  const res = await run({ filter, page: pageParam })
  total.value = res.total
  dataList.value.push(...res.list)
}

const loadMoreData = async () => {
  pageParam.pageNum += 1
  await getDataList()
}

const reloadData = async () => {
  pageParam.pageNum = 1
  dataList.value = []
  await getDataList()
}

const searchChangeHandler = async (field, filter) => {
  console.log(field, filter)
  filterMap[field] = filter
  await reloadData()
}

const operationHandler = async ({ key, data }) => {
  switch (key) {
    case 'view':
      viewRecordHandler(data)
      break
    case 'fill':
      fillRecordHandler(data)

      break
    default:
      break
  }
}

async function viewRecordHandler(record) {
  const formRecordId = record._id
  useStorageData.setStorageData(formRecordId, record)
  uni.navigateTo({
    url: `/pages_farmland/pages/formPage/formPage?formUid=${props.formUid}&formRecordId=${formRecordId}&readonly=1`,
  })
}

async function fillRecordHandler(record) {
  console.log('props.fillFormUid :>> ', props.fillFormUid)
  const formRecordId = record._id
  useStorageData.setStorageData(
    formRecordId,
    omit(record, ['_id', 'update_time', 'update_by', 'create_by', 'create_time', 'state']),
  )
  uni.navigateTo({
    url: `/pages_farmland/pages/fillFormPage/fillFormPage?formUid=${props.fillFormUid}&formRecordId=${formRecordId}`,
  })
}

onMounted(() => {
  reloadData()
})
</script>

<style lang="scss" scoped></style>
