<template>
  <SearchBar v-if="searchConfig" :config="searchConfig" @change="searchChangeHandler" />

  <view class="info bg-white p-1 px-3 flex justify-between items-center text-xs shadow-md">
    <view>共{{ total }}条</view>
    <view class="flex">
      <wd-tag
        custom-class="space"
        :type="createByMyself ? 'primary' : 'default'"
        @click="createByMyselfHandler"
        round
      >
        我的填报
      </wd-tag>
    </view>
  </view>
  <scroll-view scroll-y @scrolltolower="loadMoreData" class="bg-gray-100">
    <view class="pb-12">
      <view class="m-3" v-for="item in dataList" :key="item._id">
        <DataCard
          :data="item"
          :config="fillCardConfig"
          :userInfo="userInfo"
          @operation="operationHandler"
        ></DataCard>
      </view>
    </view>
  </scroll-view>
</template>
<script lang="ts" setup>
import formData from '@/service/form/formData'
import DataCard from '@/components/common/DataCard/index.vue'
import SearchBar from '@/components/common/SearchBar/index.vue'
import { useUserStore, useStorageDataStore } from '@/store'
import { useToast } from '@/uni_modules/wot-design-uni'

const { show: showToast } = useToast()
const useStorageData = useStorageDataStore()

const props = defineProps(['formUid', 'fillFormUid', 'searchConfig', 'cardConfig'])

const useUser = useUserStore()
const userInfo = useUser.userInfo

const DEFAULT_CARD_CONFIG = {
  title: '{_id}',
  columns: [{ label: 'createTime', field: '{create_time}', type: 'time' }],
  operations: [
    {
      key: 'view',
      label: '查看',
    },
    {
      key: 'edit',
      label: '编辑',
      depends: ['createBy', 'create_by', '_id'],
    },
  ],
}

const fillCardConfig = computed(() => {
  return props.cardConfig || DEFAULT_CARD_CONFIG
})

function removeKeysSubstring(obj, substring) {
  const result = {}
  Object.keys(obj).forEach((key) => {
    if (key.includes(substring)) {
      result[key.replace(new RegExp(substring, 'g'), '')] = obj[key]
    } else {
      result[key] = obj[key]
    }
  })
  return result
}
const pageParam = {
  pageNum: 1,
  pageSize: 10,
}
const total = ref()

const createByMyself = ref(false)

// form 子组件通用options
interface FILTER_MAP {
  [key: string]: any
}

const filterMap: FILTER_MAP = {
  state: ['!=', 'state', '待填报'],
}
const fillFiltersMap: FILTER_MAP = {}

const { loading, run } = useRequest((param) => formData.joinSearchFormData(props.formUid, param))

const dataList = ref([])
const getDataList = async () => {
  let filter = []

  Object.keys(filterMap).forEach((k) => {
    if (filterMap[k]) filter.push(filterMap[k])
  })

  if (filter.length >= 2) filter.unshift('and')

  if (filter.length === 1) filter = filter[0]

  if (!filter.length) filter = null

  let fillFilter = null
  const fillFilters = Object.values(fillFiltersMap).filter((i) => i)
  if (fillFilters.length >= 2) {
    fillFilter = ['and', ...fillFilters]
  } else if (fillFilters.length === 1) {
    fillFilter = fillFilters[0]
  }

  const res = await run({
    filter,
    page: pageParam,
    joins: [
      {
        type: 'left',
        table: props.fillFormUid,
        joinedTable: props.formUid,
        filter: fillFilter,
        resolveSubItems: true,
        multiJoinFields: [
          {
            joinedField: '_id',
            field: 'task_id',
          },
        ],
      },
    ],
  })
  total.value = res.total
  dataList.value.push(
    ...res.list.map((item) => {
      return removeKeysSubstring(item, `${props.fillFormUid}.`)
    }),
  )
  console.log('dataList.value :>> ', dataList.value)
}

const loadMoreData = async () => {
  pageParam.pageNum += 1
  await getDataList()
}

const reloadData = async () => {
  pageParam.pageNum = 1
  dataList.value = []
  await getDataList()
}
const searchChangeHandler = () => {}

const createByMyselfHandler = async () => {
  createByMyself.value = !createByMyself.value

  fillFiltersMap.createBy = createByMyself.value ? ['=', 'create_by', userInfo.user_id] : null

  await reloadData()
}

async function operationHandler({ key, data }) {
  console.log(key, data)
  switch (key) {
    case 'edit':
      editRecordHandler(data)
      break
    case 'view':
      viewRecordHandler(data)
      break
    case 'delete':
      deleteRecordHandler(data)
      break
    default:
      break
  }
}

async function editRecordHandler(record) {
  const formRecordId = record._id
  useStorageData.setStorageData(formRecordId, record)
  uni.navigateTo({
    url: `/pages_farmland/pages/formPage/formPage?formUid=${props.fillFormUid}&formRecordId=${formRecordId}`,
  })
}

async function viewRecordHandler(record) {
  const formRecordId = record._id
  useStorageData.setStorageData(formRecordId, record)
  uni.navigateTo({
    url: `/pages_farmland/pages/formPage/formPage?formUid=${props.fillFormUid}&formRecordId=${formRecordId}&readonly=1`,
  })
}

async function deleteRecordHandler(record) {
  try {
    await formData.deleteFormDataById(props.fillFormUid, record._id)
    await reloadData()
  } catch (error) {
    showToast('删除失败')
  }
}

onMounted(() => {
  reloadData()
})

// export default {
//   components: { DataCard, SearchBar },
//   data() {
//     return {
//       page: PAGE_PARAM,
//       dataList: [],
//       inputValue: null,
//       filter: null,
//       containerHeight: 0,
//       total: 0,
//       filtersMap: {
//         state: ['!=', 'state', '待填报'],
//       },
//       fillFiltersMap: {},
//       userInfo: null,
//       createByMyself: false,
//       filterReturnData: false,
//     }
//   },
//   props: {
//     formUid: String,
//     fillFormUid: String,
//     searchConfig: {
//       type: Object,
//       default: DEFAULT_SEARCH_CONFIG,
//     },
//     cardConfig: {
//       type: Object,
//       default: DEFAULT_CARD_CONFIG,
//     },
//   },
//   async mounted() {
//     const userInfo = JSON.parse(uni.getStorageSync('userInfo'))
//     this.userInfo = userInfo
//     this.$nextTick(() => {
//       uni
//         .createSelectorQuery()
//         .in(this)
//         .select('.content')
//         .boundingClientRect((rect) => {
//           console.log(rect, 'rect')
//           this.containerHeight = rect.height
//         })
//         .exec()
//     })
//     this.reloadData()
//   },
//   methods: {
//     adminChangeHandler(e) {
//       console.log(e, 'adminFilter')
//       this.$set(this.filtersMap, 'admin', e)
//       this.reloadData()
//     },
//     searchHandler(e) {
//       console.log(e, 'searchHandler')
//       this.$set(
//         this.fillFiltersMap,
//         this.searchConfig.field,
//         e ? ['like', this.searchConfig.field, `%${e}%`] : null,
//       )
//       this.reloadData()
//     },
//     reloadData() {
//       this.page.pageNum = 1
//       this.dataList = []
//       this.getList(true)
//     },
//     loadMoreData() {
//       this.page.pageNum += 1
//       this.getList()
//     },
//     async getList(reload) {
//       let filter = null
//       const filters = Object.values(this.filtersMap).filter((i) => i)
//       if (filters.length >= 2) {
//         filter = ['and', ...filters]
//       } else if (filters.length === 1) {
//         filter = filters[0]
//       }
//       let fillFilter = null
//       const fillFilters = Object.values(this.fillFiltersMap).filter((i) => i)
//       if (fillFilters.length >= 2) {
//         fillFilter = ['and', ...fillFilters]
//       } else if (fillFilters.length === 1) {
//         fillFilter = fillFilters[0]
//       }
//       const res = await this.$apis.formData.joinSearchFormData(this.formUid, {
//         filter,
//         page: this.page,
//         joins: [
//           {
//             type: 'left',
//             table: this.fillFormUid,
//             joinedTable: this.formUid,
//             filter: fillFilter,
//             resolveSubItems: true,
//             multiJoinFields: [
//               {
//                 joinedField: '_id',
//                 field: 'task_id',
//               },
//             ],
//           },
//         ],
//       })
//       const list = res.list.map((item) => {
//         return removeKeysSubstring(item, `${this.fillFormUid}.`)
//       })
//       console.log(list, 'list')
//       this.total = res.total
//       if (reload) {
//         this.dataList = list
//       } else {
//         this.dataList.push(...list)
//       }
//     },
//     operationHandler({ key, data }) {
//       switch (key) {
//         case 'view':
//           this.viewHandler(data)
//           break
//         case 'edit':
//           this.editHandler(data)
//           break
//         case 'delete':
//           this.deleteHandler(data)
//           break
//         case 'audit':
//           this.auditHandler(data)
//           break
//         default:
//           break
//       }
//     },
//     fillHandler(e) {
//       this.$emit('fillClick', e)
//     },

//     viewHandler(data) {
//       const formRecordUid = data._id
//       Storage.saveFormData(formRecordUid, data)
//       const pageUrl = `${this.$constants.PAGE.VIEW_PAGE_URL}?formUid=${this.fillFormUid}&formRecordUid=${formRecordUid}`
//       uni.navigateTo({
//         url: pageUrl,
//       })
//     },
//     editHandler(data) {
//       const formRecordUid = data._id
//       Storage.saveFormData(formRecordUid, data)
//       const pageUrl = `/app/pages/FillFormPage/index?formUid=${this.fillFormUid}&formRecordUid=${formRecordUid}&cacheable=false`
//       uni.navigateTo({
//         url: pageUrl,
//       })
//     },
//     async deleteHandler(data) {
//       try {
//         await this.$apis.formData.deleteFormRecord(this.fillFormUid, data._id)
//         await this.reloadData()
//       } catch (e) {
//         this.showError(this.$constants.MSG.DELETE_FAIL)
//       }
//     },

//     async auditHandler(data) {
//       const formRecordUid = data._id
//       Storage.saveFormData(formRecordUid, data)
//       const pageUrl = `/app/pages/FormAuditPage/index?formUid=${this.fillFormUid}&formRecordUid=${formRecordUid}`
//       uni.navigateTo({
//         url: pageUrl,
//       })
//     },

//     createByMyselfHandler() {
//       this.createByMyself = !this.createByMyself

//       this.$set(
//         this.fillFiltersMap,
//         'createBy',
//         this.createByMyself ? ['=', 'create_by', this.userInfo._id] : null,
//       )

//       this.reloadData()
//     },
//     filterReturnDataHandler() {
//       this.filterReturnData = !this.filterReturnData

//       this.$set(
//         this.fillFiltersMap,
//         'returnData',
//         this.filterReturnData ? ['=', 'state', '已退回'] : null,
//       )

//       this.reloadData()
//     },
//   },
// }
</script>

<style lang="scss" scoped></style>
