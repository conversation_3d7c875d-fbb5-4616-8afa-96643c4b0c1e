const tks = [
  '9280a716be4df212fd20ebf0f5c94dce',
  '7191874d4f84f45a31a464919da9230a',
  'a6b9ed4542685fcc5a38c3b5a34db566',
]

function getRandomItem(arr) {
  const randomIndex = Math.floor(Math.random() * arr.length)
  return arr[randomIndex]
}
export const getStyle = () => {
  const tk = getRandomItem(tks)
  return {
    version: 8,
    center: [116, 35],
    zoom: 4,
    sprite: 'http://39.104.87.15/api/basemap/sprites/sprite',
    glyphs: 'http://39.104.87.15/api/basemap/fonts/{fontstack}/{range}.pbf',
    sources: {
      'gt-basemap-vector-14-20': {
        type: 'raster',
        tiles: [
          `http://t2.tianditu.gov.cn/img_w/wmts?tk=${tk}&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles&LAYER=img`,
        ],
        maxzoom: 18,
        minzoom: 3,
      },
    },
    layers: [
      {
        id: 'gt-basemap-vector-14-20',
        type: 'raster',
        source: 'gt-basemap-vector-14-20',
      },
    ],
  }
}
