<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '地图',
  },
}
</route>
<template>
  <view class="h-screen flex flex-col">
    <wd-navbar
      :title="'地图'"
      placeholder
      safeAreaInsetTop
      leftArrow
      @click-left="navigateBack"
    ></wd-navbar>

    <SearchBar :config="searchConfig" v-if="searchConfig" @change="searchChangeHandler" />

    <view class="flex-1">
      <view class="h-full">
        <Map
          class="h-full"
          ref="mapRef"
          :filterMap="filterMap"
          v-if="projectId"
          :projectId="projectId"
        />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import SearchBar from '@/components/common/SearchBar/index.vue'
import { useBasePage } from '@/pages/useBasePage'
import { SEARCH_CONFIG } from './list.config'
import Map from './Map/index.vue'

const { navigateBack } = useBasePage()

const searchConfig = ref(SEARCH_CONFIG)
const filterMap = reactive({})
const projectId = ref('')
const mapRef = ref()
async function searchChangeHandler(field, filter) {
  console.log(field, filter)
  filterMap[field] = filter
  await mapRef.value.reloadData()
}

onShow(async () => {})

onLoad(async (e) => {
  if (e.projectId) {
    projectId.value = e.projectId
  }
})
</script>

<style lang="scss" scoped></style>
