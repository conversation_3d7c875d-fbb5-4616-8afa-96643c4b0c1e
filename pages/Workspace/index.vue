<template>
  <view class="wrapper">
    <u-navbar title="工作台" :bgColor="$constants.COLOR.PRIMARY_COLOR" placeholder></u-navbar>

    <!-- 系统选择区域 -->
    <view class="content">
      <view class="header-section">
        <view class="welcome-text">请选择要使用的系统</view>
      </view>

      <!-- 系统卡片列表 -->
      <view class="system-cards">
        <view class="system-card" v-for="(system, index) in systemList" :key="index" @click="selectSystem(system)">
          <view class="card-content">
            <view class="system-icon">
              <u-image
                :src="system.icon"
                width="120rpx"
                height="120rpx"
                mode="aspectFit"
                :showLoading="true"
                :showError="true"
                errorIcon="photo"
              ></u-image>
            </view>
            <view class="system-info">
              <view class="system-name">{{ system.name }}</view>
              <view class="system-desc">点击进入系统</view>
            </view>
            <view class="arrow-icon">
              <u-icon name="arrow-right" color="#c0c4cc" size="32rpx"></u-icon>
            </view>
          </view>

          <!-- 卡片底部装饰线 -->
          <view class="card-accent" :style="{ backgroundColor: getAccentColor(index) }"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import BasePage from "@/pages/BasePage";
import system from "@/common/system.js";
import ajax from "@/apis/ajax";

export default {
  mixins: [BasePage],

  data() {
    return {
      systemList: system,
      accentColors: ["#1677FF", "#00a88a", "#f9ae3d", "#5ac725"], // 多种颜色供选择
    };
  },

  onLoad() {
    uni.hideTabBar();
    console.log("系统列表:", this.systemList);
  },

  methods: {
    // 选择系统
    selectSystem(system) {
      this.showLoading(`正在进入...`);

      // 简单设置系统URL和缓存
      const success = ajax.setSystemURL(system);

      // 恢复系统缓存到全局缓存
      this.setStorage(system.key);

      if (success) {
        setTimeout(() => {
          this.hideLoading();
          uni.navigateTo({
            url: `/pages/Login/index?system=${system.key}`,
          });
        }, 1500);
      } else {
        uni.showToast({
          title: "系统读取失败",
          icon: "error",
          duration: 1500,
          mask: true,
        });
      }
    },
    /**
     * 恢复系统缓存到全局缓存
     * @param {string} key - 系统标识
     */
    setStorage(key) {
      if (!key) return;

      try {
        uni.setStorageSync("system_key", key);
        
        const storageStr = uni.getStorageSync("system_storage");
        if (!storageStr) return;

        const storage = JSON.parse(storageStr);
        const systemData = storage[key];

        if (!systemData) return;

        // 恢复全局缓存
        if (systemData.access_token) {
          uni.setStorageSync("access_token", systemData.access_token);
        }
        if (systemData.refresh_token) {
          uni.setStorageSync("refresh_token", systemData.refresh_token);
        }
        if (systemData.userInfo) {
          uni.setStorageSync("userInfo", JSON.stringify(systemData.userInfo));
        }
      } catch (error) {
        console.error("恢复系统缓存失败:", error);
      }
    },

    // 获取装饰色
    getAccentColor(index) {
      return this.accentColors[index % this.accentColors.length];
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title {
    color: $gt-navbar-title-color !important;
  }
}

/deep/ .u-navbar__content__left {
  display: none;
}

uni-page-body {
  height: 100%;
}

.wrapper {
  height: 100vh;
  background: #f8f8f8;
}

.content {
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
}

.header-section {
  text-align: center;
  margin-bottom: 60rpx;

  .welcome-text {
    font-size: 48rpx;
    font-weight: 600;
    color: #303133;
    margin-bottom: 20rpx;
  }
}

.system-cards {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.system-card {
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
  }
}

.card-content {
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.system-icon {
  margin-right: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
}

.system-info {
  flex: 1;

  .system-name {
    font-size: 36rpx;
    font-weight: 600;
    color: #303133;
    margin-bottom: 12rpx;
  }

  .system-desc {
    font-size: 26rpx;
    color: #909399;
  }
}

.arrow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}

.card-accent {
  height: 8rpx;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
}
</style>
