<template>
  <view class="wrapper">
    <!-- 系统选择区域 -->
    <view class="content">
      <view class="header-section">
        <view class="welcome-text">工作台</view>
      </view>
      <!-- 系统卡片列表 -->
      <view class="system-cards">
        <view class="system-card" v-for="(system, index) in systemList" :key="index" @click="selectSystem(system)">
          <view class="card-content">
            <image class="system-icon" :src="system.icon" mode="aspectFill"></image>
            <view class="system-name">{{ system.name }}</view>
            <view class="system-info">
              <view class="system-desc">点击进入系统</view>
              <view class="arrow-icon">
                <u-icon name="arrow-right" color="#c0c4cc" size="28rpx"></u-icon>
              </view>
            </view>
          </view>

          <!-- 卡片底部装饰线 -->
          <view class="card-accent" :style="{ backgroundColor: getAccentColor(index) }"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import BasePage from "@/pages/BasePage";
import system from "@/common/system.js";
import ajax from "@/apis/ajax";

export default {
  mixins: [BasePage],

  data() {
    return {
      systemList: system,
      accentColors: ["#1677FF", "#00a88a", "#f9ae3d", "#5ac725"], // 多种颜色供选择
    };
  },

  onLoad() {
    uni.hideTabBar();
    console.log("系统列表:", this.systemList);
  },

  methods: {
    // 选择系统
    selectSystem(system) {
      this.showLoading(`正在进入...`);

      // 简单设置系统URL和缓存
      const success = ajax.setSystemURL(system);

      // 恢复系统缓存到全局缓存
      this.setStorage(system.key);

      if (success) {
        setTimeout(() => {
          this.hideLoading();
          uni.navigateTo({
            url: `/pages/Login/index?system=${system.key}`,
          });
        }, 1500);
      } else {
        uni.showToast({
          title: "系统读取失败",
          icon: "error",
          duration: 1500,
          mask: true,
        });
      }
    },
    /**
     * 恢复系统缓存到全局缓存
     * @param {string} key - 系统标识
     */
    setStorage(key) {
      if (!key) return;

      try {
        uni.setStorageSync("system_key", key);

        const storageStr = uni.getStorageSync("system_storage");
        if (!storageStr) return;

        const storage = JSON.parse(storageStr);
        const systemData = storage[key];

        if (!systemData) return;

        // 恢复全局缓存
        if (systemData.access_token) {
          uni.setStorageSync("access_token", systemData.access_token);
        }
        if (systemData.refresh_token) {
          uni.setStorageSync("refresh_token", systemData.refresh_token);
        }
        if (systemData.userInfo) {
          uni.setStorageSync("userInfo", JSON.stringify(systemData.userInfo));
        }
      } catch (error) {
        console.error("恢复系统缓存失败:", error);
      }
    },

    // 获取装饰色
    getAccentColor(index) {
      return this.accentColors[index % this.accentColors.length];
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title {
    color: $gt-navbar-title-color !important;
  }
}

/deep/ .u-navbar__content__left {
  display: none;
}

uni-page-body {
  height: 100%;
}

.wrapper {
  height: 100vh;
  background-image: linear-gradient(to bottom, $gt-primary-color, $gt-primary-color 20vh, #f8f8f8 40vh);
}

.content {
  padding: 40rpx 30rpx;
}

.header-section {
  text-align: center;
  margin: 60rpx 0;

  .welcome-text {
    font-size: 48rpx;
    font-weight: 600;
    color: #fff;
    margin-bottom: 20rpx;
  }
}

.system-cards {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 100rpx;
}

.system-card {
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  width: 100%;
}

.card-content {
  padding: 30rpx;
}

.system-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
}

.system-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-top: 10rpx;
}
.system-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  margin-top: 20rpx;

  .system-desc {
    font-size: 26rpx;
    color: #909399;
  }
}

.card-accent {
  height: 12rpx;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
}
</style>
