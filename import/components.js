/**
 * 注册表单组件
 */

import Vue from "vue";
import system from "@/common/system";

// 注册通用表单组件
const components = require.context("@/components/form", true, /.vue$/);

let map = {};

components.keys().forEach((fileName) => {
  let component = components(fileName);
  const compPath = fileName.split("/");
  map[compPath[compPath.length - 2]] = component.default;
});

Object.keys(map).forEach((key) => {
  Vue.component(key, map[key]);
  console.log(`已导入通用组件: ${key}`, map[key]);
});

// 注册自定义组件
system.forEach((item) => {
  const systemKey = item.key;
  const currentSystemKey = uni.getStorageSync("system_key");

  // 只处理当前激活的系统
  if (currentSystemKey && systemKey !== currentSystemKey) return;

  try {
    if (!item.customFields) return;

    // 使用新的路径扫描自定义组件
    const customComponents = require.context("@/components/custom/", true, /\w+\/.+\.vue$/);

    let customMap = {};

    customComponents.keys().forEach((fileName) => {
      // 检查是否属于当前系统
      if (!fileName.includes(`${systemKey}/`)) {
        return;
      }

      let component = customComponents(fileName);
      const pathParts = fileName.split("/");

      // 获取组件文件夹名称（systemKey 后的第一级目录）
      const systemIndex = pathParts.findIndex((part) => part === systemKey);
      if (systemIndex !== -1 && systemIndex + 1 < pathParts.length) {
        const componentName = pathParts[systemIndex + 1];
        const registeredName = `${systemKey}-${componentName}`;
        customMap[registeredName] = component.default;
      }
    });

    Object.keys(customMap).forEach((key) => {
      Vue.component(key, customMap[key]);
      console.log("已导入自定义组件:", key, customMap[key]);
    });
  } catch (error) {
    console.warn("导入自定义组件失败:", error);
  }
});
