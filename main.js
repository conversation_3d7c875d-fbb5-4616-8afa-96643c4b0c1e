import Vue from "vue";
import App from "./App";

import uView from "uview-ui";
import utils from "@/utils";
import events from "@/events";
import constants from "@/constants";
import apis from "@/apis";

// 注册组件  =======================================================

// 表单组件
import CascaderField from "gt-mis-app-components/components/CascaderField";
import CheckboxField from "gt-mis-app-components/components/CheckboxField";
import DateField from "gt-mis-app-components/components/DateField";
import DateTimeField from "gt-mis-app-components/components/DateTimeField";
import IntegerField from "gt-mis-app-components/components/IntegerField";
import LonLatField from "gt-mis-app-components/components/LonLatField";
import NumberField from "gt-mis-app-components/components/NumberField";
import NumberWithUnitField from "gt-mis-app-components/components/NumberWithUnitField";
import PictureField from "gt-mis-app-components/components/PictureField";
import RadioField from "gt-mis-app-components/components/RadioField";
import SelectField from "gt-mis-app-components/components/SelectField";
import SubForm from "gt-mis-app-components/components/SubForm";
import TextField from "gt-mis-app-components/components/TextField";
import GTForm from "@/components/form/GTForm";

// 布局组件
import Grid from "gt-mis-app-components/components/Grid";
import SplitLine from "gt-mis-app-components/components/SplitLine";
import Title from "gt-mis-app-components/components/Title";

// 子系统定制组件
import plowlandCropInputField from "@/pages_plowland/components/Field/CropInputField";
import plowlandPictureField from "@/pages_plowland/components/Field/PictureField";
import plowlandTextField from "@/pages_plowland/components/Field/TextField";
import plowlandTextAreaField from "@/pages_plowland/components/Field/TextAreaField";

Vue.component("CascaderField", CascaderField);
Vue.component("CheckboxField", CheckboxField);
Vue.component("DateField", DateField);
Vue.component("DateTimeField", DateTimeField);
Vue.component("IntegerField", IntegerField);
Vue.component("LonLatField", LonLatField);
Vue.component("NumberField", NumberField);
Vue.component("NumberWithUnitField", NumberWithUnitField);
Vue.component("PictureField", PictureField);
Vue.component("RadioField", RadioField);
Vue.component("SelectField", SelectField);
Vue.component("SubForm", SubForm);
Vue.component("TextField", TextField);
Vue.component("GTForm", GTForm);

Vue.component("Grid", Grid);
Vue.component("SplitLine", SplitLine);
Vue.component("Title", Title);

// 注册子系统组件  =======================================================
Vue.component("plowland-CropInputField", plowlandCropInputField);
Vue.component("plowland-PictureField", plowlandPictureField);
Vue.component("plowland-TextField", plowlandTextField);
Vue.component("plowland-TextAreaField", plowlandTextAreaField);

// 注册扩展组件  ====================================================

Vue.use(uView);
Vue.use(utils);
Vue.use(events);
Vue.use(constants);
Vue.use(apis);

App.mpType = "app";
const app = new Vue({
  ...App,
});

app.$mount();
