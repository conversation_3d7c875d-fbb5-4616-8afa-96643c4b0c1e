import ComparatorFactory from "./GTForm/comparator/Factory"

export default {
	// 注入表单级EventBus
	inject: ["bus"],

	props: {
		options: {
			type: Object,
		},
	},

	computed: {
	  formData() {
	    return this.options.formData
	  },
	  depends() {
	    return this.options.depends
	  }
	},
	
	created() {
	  if (this.depends) {
	    this.updateVisibility()
	  }
	  this.bus.$on(this.$events.form.FIELD_CHANGE, this.formChangeHandler)
	},
	
	methods: {
	  // 自动显示或隐藏
	  updateVisibility() {
	    const comparator = ComparatorFactory.createComparator(
	      this.depends,
	      this.formData
	    )
	    const visible = comparator.compare()
	    this.$set(this.options, 'visible', visible)
	  },
	  //
	  formChangeHandler(payload) {
			const { field, data, value } = payload
			this.formData[field] = value
	    // 处理deponds逻辑
			if (this.depends && this.fieldInDepands(field, this.depends)) {
				this.updateVisibility()
	    }
	  },
	
	  fieldInDepands(field, depends) {
	    let res = false
	    for (var i = 0; i < depends.length; i++) {
	      if (Array.isArray(depends[i])) {
	        //判断是否为多维数组
	        res = this.fieldInDepands(field, depends[i])
	        if (res) return true
	      } else {
	        if (depends[i] === field) return true
	      }
	    }
	    return res
	  }
	}
}