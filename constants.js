import Vue from "vue";

const constants = {
  MSG: {
    DATA_SUBMITTING: "数据提交中...",
    DATA_SUBMIT_SUCCESS: "数据已提交",
    DATA_SUBMIT_FAIL: "数据提交失败",
    DATA_CACHED_SUCCESS: "数据已缓存",
    DATA_CACHED_FAIL: "数据缓存失败",
    //
    DELETING: "数据删除中...",
    DELETE_SUCCESS: "删除成功",
    DELETE_FAIL: "删除失败",
    //
    WARNING_TITLE: "提醒",
    WARNING_DATA_SAVE: "数据未保存，是否确定退出此页面？",
    WARNING_LOST_LOCATION: "请先完成定位",
    WARNING_DATA_DELETE: "是否确定删除该数据？",

    //
    GET_FORM_DEF_FAIL: "获取表单定义失败",

    //
    UPLOADING: "上传中...",
    UPLOAD_PICTURE_FAIL: "上传图片失败",

    //
    LOCATION_LOADING: "定位中...",
    LOCATION_FAIL: "定位失败",
  },

  COLOR: {
    PRIMARY_COLOR: "#3c9cff",
    ERROR_COLOR: "#f56c6c",
  },

  PATH: {
    FILE_CACHE_PATH: "cache_file/",
  },

  PAGE: {
    FORM_PAGE_URL: "/pages/FormPage/index",
    FORM_LIST_URL: "/pages/FormList/index",
    VIEW_PAGE_URL: "/pages/ViewPage/index",
    SUBFORM_PAGE_URL: "/components/form/field/SubForm/SubFormPage/index",
  },

  VERSION: {
    CHECK: "正在检查新版本...",
    NEW_VERSION: "检查到新版本，是否升级？",
    FAILED: "检查新版本失败",
    LATEST_ALREADY: "已是最新版本",
  },
};

export default {
  install() {
    Vue.prototype.$constants = constants;
  },
};
